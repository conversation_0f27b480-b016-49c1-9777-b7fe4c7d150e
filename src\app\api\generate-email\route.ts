import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/jwt-middleware';
import { db } from '@/lib/db';
import { generatedEmails } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function handleGenerateEmail(request: AuthenticatedRequest) {
  try {
    // 读取名字列表
    const namesFilePath = path.join(process.cwd(), 'namelist', 'names.txt');
    const namesContent = fs.readFileSync(namesFilePath, 'utf-8');
    const names = namesContent.split('\n').filter(name => name.trim());

    let email: string;
    let attempts = 0;
    const maxAttempts = 100;

    do {
      // 随机选择一个名字
      const randomName = names[Math.floor(Math.random() * names.length)];
      
      // 生成6位随机数字
      const randomNumbers = Math.floor(100000 + Math.random() * 900000).toString();
      
      // 处理名字：移除空格，转换为小写
      const cleanName = randomName.replace(/\s+/g, '').toLowerCase();
      
      // 随机决定数字的位置：前面、中间或后面
      const position = Math.floor(Math.random() * 3);
      let emailPrefix: string;
      
      if (position === 0) {
        // 数字在前面
        emailPrefix = randomNumbers + cleanName;
      } else if (position === 1) {
        // 数字在中间
        const midPoint = Math.floor(cleanName.length / 2);
        emailPrefix = cleanName.substring(0, midPoint) + randomNumbers + cleanName.substring(midPoint);
      } else {
        // 数字在后面
        emailPrefix = cleanName + randomNumbers;
      }
      
      email = emailPrefix + '@techexpresser.com';
      
      // 检查数据库中是否已存在
      const existing = await db.select().from(generatedEmails).where(eq(generatedEmails.email, email)).limit(1);
      
      if (existing.length === 0) {
        break; // 找到唯一的邮箱
      }
      
      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      return NextResponse.json(
        { success: false, error: '无法生成唯一邮箱，请重试' },
        { status: 500 }
      );
    }

    // 保存到数据库
    const [newEmail] = await db.insert(generatedEmails).values({
      email,
    }).returning();

    return NextResponse.json({
      success: true,
      email: newEmail,
    });
  } catch (error) {
    console.error('Error generating email:', error);
    return NextResponse.json(
      { success: false, error: '生成邮箱失败' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  return withAuth(request, handleGenerateEmail);
}
