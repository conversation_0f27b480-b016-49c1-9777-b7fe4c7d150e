# One Mail

一个基于 Next.js 和 Keycloak 的邮件管理应用程序，支持 SSR（服务端渲染）和 JWT 认证。

## 功能特性

- 🔐 **Keycloak 集成**: 使用 NextAuth.js 与 Keycloak 对接
- 🛡️ **角色验证**: 验证用户是否有 `one-mail-admin` 角色
- 🔄 **自动 Token 刷新**: 智能处理 access token 过期和刷新
- 🚀 **SSR 支持**: 服务端渲染，提供更好的性能和 SEO
- 🔒 **API 保护**: 所有 API 路由都受 JWT 中间件保护
- 📱 **响应式设计**: 支持桌面和移动设备

## 技术栈

- **前端**: Next.js 15, React 19, TailwindCSS
- **认证**: NextAuth.js, Keycloak
- **数据库**: PostgreSQL 17 (可选)
- **ORM**: Drizzle (可选)
- **部署**: Node.js, PM2

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env.local` 文件并根据你的 Keycloak 配置进行修改：

```bash
# Keycloak 配置
KEYCLOAK_URL=https://your-keycloak-server.com
KEYCLOAK_CLIENT_ID=your-client-id
KEYCLOAK_CLIENT_SECRET=your-client-secret
KEYCLOAK_REQUIRED_ROLE=one-mail-admin

# 应用配置
NEXTAUTH_URL=http://localhost:9042
NEXTAUTH_SECRET=your-secret-key
```

### 3. 运行开发服务器

```bash
npm run dev
```

应用将在 [http://localhost:9042](http://localhost:9042) 启动。

## 认证流程

### 认证流程说明

1. **用户访问**: 用户访问受保护的页面
2. **重定向登录**: 未认证用户被重定向到 Keycloak 登录页面
3. **授权码交换**: 登录成功后，使用授权码换取 tokens
4. **角色验证**: 验证用户是否有必需的角色 (`one-mail-admin`)
5. **Token 管理**:
   - Access token (2-5 分钟有效期) 用于 API 调用
   - Refresh token (30 天有效期) 存储在 httpOnly cookie
   - 自动刷新机制确保用户体验流畅

## 项目结构

```
src/
├── app/
│   ├── api/
│   │   ├── auth/[...nextauth]/     # NextAuth API 路由
│   │   └── emails/                 # 邮件 API (受保护)
│   ├── auth/
│   │   ├── signin/                 # 登录页面
│   │   └── error/                  # 错误页面
│   ├── test-auth/                  # 认证测试页面
│   └── page.tsx                    # 主页面
├── components/
│   ├── EmailList.tsx               # 邮件列表组件
│   └── SessionProvider.tsx         # Session 提供者
├── lib/
│   ├── auth.ts                     # NextAuth 配置
│   └── jwt-middleware.ts           # JWT 验证中间件
└── middleware.ts                   # 路由保护中间件
```

## API 路由保护

所有 API 路由都通过 `withAuth` 中间件保护：

```typescript
export async function GET(request: NextRequest) {
  return withAuth(request, handleGetEmails);
}
```

中间件会：

- 验证 JWT token 有效性
- 检查用户角色权限
- 处理 token 刷新
- 返回适当的错误响应

## 部署

详细的部署说明请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)。

### 生产环境注意事项

1. **域名配置**: 确保 `NEXTAUTH_URL` 使用生产域名
2. **Keycloak 配置**: 添加生产域名到重定向 URI
3. **反向代理**: 配置 Nginx/Apache 代理到 localhost:9042
4. **安全密钥**: 生成安全的 `NEXTAUTH_SECRET`

## 测试

访问 `/test-auth` 页面可以查看当前用户的认证状态和角色信息。

## 故障排除

常见问题和解决方案请参考 [DEPLOYMENT.md](./DEPLOYMENT.md#故障排除)。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
