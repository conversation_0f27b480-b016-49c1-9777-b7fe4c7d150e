{"name": "one-mail", "version": "0.1.0", "private": true, "scripts": {"dev": "npx drizzle-kit generate && npx drizzle-kit migrate && next dev -p 9042", "build": "next build", "start": "next start -p 9042 ", "lint": "next lint", "db:generate": "npx drizzle-kit generate", "db:migrate": "npx drizzle-kit migrate", "prod": "npm run migrate && npm run build && npm run start"}, "dependencies": {"@google-cloud/local-auth": "^3.0.1", "@types/jsonwebtoken": "^9.0.10", "@types/pg": "^8.15.5", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "googleapis": "^155.0.1", "jose": "^6.0.12", "jsonwebtoken": "^9.0.2", "next": "15.4.6", "next-auth": "^4.24.11", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "tailwindcss": "^4", "typescript": "^5"}}