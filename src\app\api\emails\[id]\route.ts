import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/jwt-middleware';
import { getAuthorizedClient, getMessageBody } from '@/lib/gmail';

async function handleGetEmailBody(
  request: AuthenticatedRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: messageId } = await params;

    if (!messageId) {
      return NextResponse.json(
        { success: false, error: 'Message ID is required' },
        { status: 400 }
      );
    }

    // 这里你可以使用 request.user 来获取用户信息
    // 目前保持使用 Gmail API，但你可以根据需要修改
    const auth = await getAuthorizedClient();
    const body = await getMessageBody(auth, messageId);

    return NextResponse.json({ success: true, body });
  } catch (error) {
    console.error('Error fetching email body:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch email body' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  return withAuth(request, (req) => handleGetEmailBody(req, context));
}
