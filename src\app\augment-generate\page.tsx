import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import Layout from "@/components/Layout";
import AugmentGenerate from "@/components/AugmentGenerate";

export default async function AugmentGeneratePage(): Promise<JSX.Element> {
  const session = await getServerSession(authOptions);

  // 检查用户是否已登录
  if (!session) {
    redirect("/auth/signin");
  }

  // 检查会话是否有错误
  if (session.error === "RefreshAccessTokenError") {
    redirect("/auth/signin?error=session_expired");
  }

  // 检查用户是否有必需的角色
  if (!session.hasRequiredRole) {
    redirect("/auth/signin?error=insufficient_role");
  }

  return (
    <Layout>
      <AugmentGenerate />
    </Layout>
  );
}
