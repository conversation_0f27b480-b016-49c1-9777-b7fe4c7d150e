import { redirect } from "next/navigation";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import Layout from "@/components/Layout";
import EmailList from "@/components/EmailList";

export default async function Home(): Promise<JSX.Element> {
  const session = await getServerSession(authOptions);

  // 检查用户是否已登录
  if (!session) {
    redirect("/auth/signin");
  }

  // 检查会话是否有错误
  if (session.error === "RefreshAccessTokenError") {
    redirect("/auth/signin?error=session_expired");
  }

  // 检查用户是否有必需的角色
  if (!session.hasRequiredRole) {
    redirect("/auth/signin?error=insufficient_role");
  }

  // 使用Layout包装EmailList
  return (
    <Layout>
      <EmailList />
    </Layout>
  );
}
