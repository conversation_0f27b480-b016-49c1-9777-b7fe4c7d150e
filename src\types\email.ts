/**
 * 邮件消息类型定义
 */
export interface EmailMessage {
  id: string;
  threadId: string;
  subject: string;
  from: string;
  to: string;
  date: string;
  snippet: string;
  body?: string;
  isRead: boolean;
}

/**
 * API 响应类型
 */
export interface EmailListResponse {
  success: boolean;
  emails?: EmailMessage[];
  messages?: EmailMessage[]; // 兼容不同的 API 响应格式
  error?: string;
}

export interface EmailDetailResponse {
  success: boolean;
  body?: string;
  error?: string;
}
